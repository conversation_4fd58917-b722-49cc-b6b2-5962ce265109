import OpenAI from "openai";
const { apiKey, baseURL } = require('../config/serve');


const openai = new OpenAI({
    baseURL,
    apiKey
});

async function main() {
    const completion = await openai.chat.completions.create({
        messages: [{ role: "system", content: "You are a helpful assistant." }],
        model: "deepseek-chat", // deepseekV3
        // model:'deepseek-reasoner' deepseekV1
    });

    console.log(completion.choices[0].message.content);
}

main();