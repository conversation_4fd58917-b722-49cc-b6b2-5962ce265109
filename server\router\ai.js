const OpenAI = require("openai");
const express = require("express")
const router = express.Router();
const Conver = require('../modules/aiConversation')
const { apiKey, baseUrl } = require('../config/serve');
const openai = new OpenAI({
    baseURL: baseUrl,
    apiKey
});
// 检查对话有没有超token，获取db历史问ai，ai答完将新数据存入db
router.post('/aiChat', (req, res) => {
    try {

        const userid = req.session.userid;
        if (!userid) {
            return res.json({
                code: 401,
                message: '请先登录'
            });
        }
        if (!req.body) {
            return res.json({
                code: 400,
                message: '请求体结构缺失'
            })
        }
        const { newMsg } = req.body?.message;
        if (!newMsg) {
            return res.json({
                code: 400,
                message: '空内容'
            })
        }
        const { message: history, createTime } = Conver.findOne({ userid: userid });
        // 当第一次:
        if (!history) {
            const newConversation = new Conver({
                userid: userid,
                message: []
            });
            newConversation.save();
        }
        aiChat(newMsg, history);

    } catch (err) {
        console.log(err)
        res.status(500).json({
            code: 500,
            message: '服务器内部错误'
        })
    }

})


async function aiChat(message, history) {
    const completion = await openai.chat.completions.create({
        messages: [
            { role: "system", content: "You are a helpful assistant." },
            ...history.silce(-10).map(item => {
                return { role: item.role, content: item.content }
            }),
            { role: "user", content: message }
        ],
        model: "deepseek-chat", // deepseekV3
        // model:'deepseek-reasoner' deepseekV1
        stream: true // 开启stream流 
    });
    let fullResult = '';
    for await (const chunk of completion) {
        const delta = chunk.choices[0]?.delta?.content || "";
        process.stdout.write(delta); // 实时输出到控制台
        fullResult += delta;
    }
    console.log(`result=${fullResult}`);
    // 将新内容存进db
}
module.exports = router;
