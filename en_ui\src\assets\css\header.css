.user-profile {
    position: relative;
}

.avatar-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px;
    border-radius: 20px;
    transition: background-color 0.3s;
}

.avatar-container:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
    object-fit: cover;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-width: 150px;
    margin-top: 5px;
    z-index: 1000;
}

.dropdown-item {
    display: block;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s;
}

.dropdown-item:hover {
    background-color: #f5f5f5;
}

.dropdown-divider {
    height: 1px;
    background-color: #eee;
    margin: 5px 0;
}

button.dropdown-item {
    width: 100%;
    text-align: left;
    border: none;
    background: none;
    font: inherit;
    cursor: pointer;
    color: #ff4d4f;
}