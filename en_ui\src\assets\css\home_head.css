/* 全局样式 */
.home-container {
    font-family: 'Arial', sans-serif;
    color: #333;
    line-height: 1.6;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo h1 {
    color: #4285f4;
    margin: 0;
    font-size: 1.8rem;
}

.main-nav ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.main-nav li {
    margin: 0 15px;
}

.main-nav a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.main-nav a:hover,
.main-nav a.active {
    color: #4285f4;
}

.user-actions {
    display: flex;
    gap: 10px;
}

.btn-login,
.btn-register,
.btn-logout {
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s;
}


.btn-login,
.btn-logout {
    color: #4285f4;
    border: 1px solid #4285f4;
}

.username {
    color: #4285f4;
    font-weight: 500;
    font-size: 1.2rem;
}

.btn-register {
    background-color: #4285f4;
    color: white;
    border: 1px solid #4285f4;
}

.btn-login:hover,
.btn-register:hover {
    opacity: 0.9;
}

/* 英雄区域 */
.hero {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 5%;
    background-color: #f8f9fa;
}

.hero-content {
    max-width: 600px;
}

.hero-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.hero-content p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: #666;
}

.btn-primary {
    padding: 12px 24px;
    background-color: #4285f4;
    color: white;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-primary:hover {
    background-color: #3367d6;
}

.hero-image {
    width: 400px;
    height: 300px;
    background-color: #ddd;
    /* 占位用，实际使用时替换为实际图片 */
}

/* 功能介绍区域 */
section {
    padding: 60px 5%;
}

.section-title {
    text-align: center;
    margin-bottom: 40px;
    font-size: 2rem;
    color: #333;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature-card {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.feature-card h3 {
    margin: 10px 0;
    color: #4285f4;
}

/* 统计区域 */
.stats {
    background-color: #4285f4;
    color: white;
}

.stats .section-title {
    color: white;
}

.stats-container {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    padding: 20px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 1.1rem;
}

/* 今日建议 */
.suggestion-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.card {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card h3 {
    color: #4285f4;
    margin-top: 0;
}

.btn-secondary {
    padding: 8px 16px;
    background-color: transparent;
    color: #4285f4;
    border: 1px solid #4285f4;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-secondary:hover {
    background-color: #4285f4;
    color: white;
}

/* 页脚 */
.footer {
    background-color: #333;
    color: white;
    padding: 40px 5% 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h3 {
    color: #4285f4;
    margin-top: 0;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.copyright {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #555;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero {
        flex-direction: column;
        text-align: center;
    }

    .hero-image {
        margin-top: 30px;
        width: 100%;
        height: 200px;
    }

    .header {
        flex-direction: column;
        padding: 10px;
    }

    .main-nav {
        margin: 15px 0;
    }

    .main-nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }
}