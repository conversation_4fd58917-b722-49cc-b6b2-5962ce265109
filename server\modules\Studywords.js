const mongoose = require('mongoose');
let studyWordsSchema = new mongoose.Schema({
    userId: {
        type: String,
        required: true,
    },
    // 目前只用于存储cet4，存储够用
    wordsStack: {
        type: [[mongoose.Schema.Types.ObjectId]],
        default: []
    }
    ,
    lastUpdate: {
        type: Date,
        default: Date.now
    }
});
const StudyWords = mongoose.model('StudyWords', studyWordsSchema);
module.exports = StudyWords;