2025-08-07 20:45:05 [ERROR]: TypeError: Cannot read properties of undefined (reading 'charAt')
    at C:\Users\<USER>\Desktop\BBBBig\server\router\commendWords.js:34:39
    at Array.map (<anonymous>)
    at C:\Users\<USER>\Desktop\BBBBig\server\router\commendWords.js:27:43
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
2025-08-07 20:47:04 [ERROR]: TypeError: Cannot read properties of undefined (reading 'charAt')
    at C:\Users\<USER>\Desktop\BBBBig\server\router\commendWords.js:36:39
    at Array.map (<anonymous>)
    at C:\Users\<USER>\Desktop\BBBBig\server\router\commendWords.js:27:43
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
2025-08-07 20:48:50 [ERROR]: TypeError: Cannot read properties of undefined (reading 'charAt')
    at C:\Users\<USER>\Desktop\BBBBig\server\router\commendWords.js:36:39
    at Array.map (<anonymous>)
    at C:\Users\<USER>\Desktop\BBBBig\server\router\commendWords.js:27:43
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
2025-08-07 20:49:30 [ERROR]: TypeError: Cannot read properties of undefined (reading 'charAt')
    at C:\Users\<USER>\Desktop\BBBBig\server\router\commendWords.js:36:39
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Promise.all (index 0)
    at async C:\Users\<USER>\Desktop\BBBBig\server\router\commendWords.js:27:15
2025-08-08 15:50:38 [ERROR]: TypeError: Assignment to constant variable.
    at C:\Users\<USER>\Desktop\BBBBig\server\router\commendWords.js:26:15
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
2025-08-08 21:17:08 [ERROR]: 未处理的Promise拒绝
2025-08-08 21:18:56 [ERROR]: 未处理的Promise拒绝
2025-08-09 14:34:56 [ERROR]: Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:699:11)
    at ServerResponse.header (C:\Users\<USER>\Desktop\BBBBig\server\node_modules\express\lib\response.js:684:10)
    at ServerResponse.send (C:\Users\<USER>\Desktop\BBBBig\server\node_modules\express\lib\response.js:161:12)
    at ServerResponse.json (C:\Users\<USER>\Desktop\BBBBig\server\node_modules\express\lib\response.js:250:15)
    at C:\Users\<USER>\Desktop\BBBBig\server\router\ai.js:55:25
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
