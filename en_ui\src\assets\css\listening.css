/* 听力训练页面样式 */
.listening-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
}

/* 头部区域 */
.listening-header {
    background: rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px 0;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin: 0 0 20px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.progress-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.progress-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    font-weight: 500;
}

.progress-bar {
    width: 300px;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(79, 172, 254, 0.5);
}

/* 主要内容区域 */
.listening-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
}

/* 加载状态 */
.loading-state {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

/* 没有单词状态 */
.no-words-state {
    text-align: center;
    color: white;
}

.no-words-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    padding: 40px;
    max-width: 500px;
    width: 100%;
    color: #333;
}

.no-words-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.no-words-content h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin: 0 0 16px 0;
}

.no-words-message {
    font-size: 1.1rem;
    color: #666;
    margin: 0 0 30px 0;
    line-height: 1.5;
}

.no-words-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 听力练习区域 */
.listening-exercise {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    padding: 40px;
    max-width: 800px;
    width: 100%;
    text-align: center;
}

/* 音频播放区域 */
.audio-section {
    margin-bottom: 40px;
}

.audio-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.play-btn {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-btn:hover:not(:disabled) {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.play-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.play-btn.playing {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.audio-info {
    text-align: left;
}

.instruction {
    font-size: 1.1rem;
    color: #333;
    margin: 0 0 8px 0;
    font-weight: 500;
}

.instruction.no-audio {
    color: #ff6b35;
    font-weight: 600;
    background: rgba(255, 107, 53, 0.1);
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.hint {
    font-size: 1rem;
    color: #666;
    margin: 0;
    font-style: italic;
}

.audio-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.hint-btn,
.replay-btn {
    padding: 8px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 20px;
    background: white;
    color: #666;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.hint-btn:hover,
.replay-btn:hover {
    border-color: #667eea;
    color: #667eea;
}

.hint-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* 输入区域 */
.input-section {
    margin-bottom: 40px;
}

.letters-container {
    display: flex;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.letter-slot {
    position: relative;
    width: 50px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid transparent;
}

.letter-slot.active {
    transform: scale(1.05);
    background: #e3f2fd;
    border-color: #2196f3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
}

.letter-slot.filled {
    background: #e3f2fd;
    border-color: #90caf9;
}

.letter-slot.correct {
    background: #e8f5e8;
    border-color: #4caf50;
}

.letter-slot.incorrect {
    background: #ffebee;
    border-color: #f44336;
    animation: shake 0.5s ease-in-out;
}

.user-letter {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    text-transform: lowercase;
    position: absolute;
    top: 15px;
    font-family: 'Courier New', monospace;
}

.letter-slot.correct .user-letter {
    color: #2e7d32;
}

.letter-slot.incorrect .user-letter {
    color: #c62828;
}

.underline {
    position: absolute;
    bottom: 15px;
    width: 35px;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.letter-slot.active .underline {
    background: #2196f3;
    transform: scaleX(1.1);
    box-shadow: 0 0 8px rgba(33, 150, 243, 0.4);
}

.letter-slot.filled .underline {
    background: #90caf9;
}

.letter-slot.correct .underline {
    background: #4caf50;
}

.letter-slot.incorrect .underline {
    background: #f44336;
}

@keyframes shake {

    0%,
    100% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-4px);
    }

    75% {
        transform: translateX(4px);
    }
}

.input-info {
    text-align: center;
    color: #666;
}

.input-hint {
    font-size: 0.9rem;
    margin: 0 0 5px 0;
}

.input-progress {
    font-size: 0.9rem;
    margin: 0;
    font-weight: 500;
}

/* 结果显示区域 */
.result-section {
    margin-bottom: 20px;
}

.result-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    padding: 24px;
    border-radius: 16px;
    margin-bottom: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-message.success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 2px solid #28a745;
    color: #155724;
}

.result-message.error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border: 2px solid #dc3545;
    color: #721c24;
}

.result-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.result-text {
    text-align: left;
}

.result-text h3 {
    margin: 0 0 8px 0;
    font-size: 1.3rem;
}

.result-text p {
    margin: 4px 0;
    font-size: 1rem;
}

.word-meaning {
    font-style: italic;
    color: #666 !important;
}

/* 按钮样式 */
.action-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-width: 140px;
}

.btn-secondary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    border: 2px solid #dee2e6;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 2px solid transparent;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* 完成状态 */
.completion-state {
    text-align: center;
    color: white;
}

.completion-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    padding: 40px;
    max-width: 600px;
    width: 100%;
    color: #333;
}

.completion-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.completion-content h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin: 0 0 30px 0;
}

.completion-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
    gap: 20px;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
}

.completion-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .listening-container {
        padding: 0;
    }

    .title {
        font-size: 2rem;
    }

    .progress-bar {
        width: 250px;
    }

    .listening-exercise,
    .completion-content,
    .no-words-content {
        margin: 20px;
        padding: 30px 20px;
    }

    .audio-controls {
        flex-direction: column;
        gap: 15px;
    }

    .play-btn {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }

    .letters-container {
        gap: 8px;
    }

    .letter-slot {
        width: 40px;
        height: 60px;
    }

    .user-letter {
        font-size: 1.5rem;
        top: 12px;
    }

    .underline {
        width: 30px;
        bottom: 12px;
    }

    .result-message {
        flex-direction: column;
        text-align: center;
    }

    .result-text {
        text-align: center;
    }

    .action-buttons,
    .completion-actions,
    .no-words-actions {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }

    .completion-stats {
        flex-direction: column;
        gap: 15px;
    }

    .audio-actions {
        flex-direction: column;
        gap: 8px;
    }
}

/* 虚拟键盘样式 */
.virtual-keyboard {
    margin: 20px 0;
    padding: 15px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.keyboard-row {
    display: flex;
    justify-content: center;
    gap: 6px;
    margin-bottom: 8px;
}

.keyboard-row:last-child {
    margin-bottom: 0;
}

.key-btn {
    min-width: 35px;
    height: 45px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    -webkit-user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.key-btn:hover {
    background: #f0f0f0;
    border-color: #ccc;
    transform: translateY(-1px);
}

.key-btn:active {
    transform: translateY(0);
    background: #e0e0e0;
}

.key-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.key-btn:disabled:hover {
    background: white;
    border-color: #ddd;
    transform: none;
}

.letter-key {
    flex: 1;
    max-width: 40px;
}

.action-key {
    padding: 0 12px;
    font-size: 14px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.action-key:hover:not(:disabled) {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.action-key:active:not(:disabled) {
    transform: translateY(0);
    background: linear-gradient(135deg, #4e5fc6 0%, #5e377e 100%);
}

/* 提示文字响应式 */
.desktop-hint {
    display: inline;
}

.mobile-hint {
    display: none;
}

/* 移动端样式调整 */
@media (max-width: 768px) {
    .virtual-keyboard {
        margin: 15px 0;
        padding: 10px;
    }

    .key-btn {
        min-width: 30px;
        height: 40px;
        font-size: 14px;
    }

    .keyboard-row {
        gap: 4px;
        margin-bottom: 6px;
    }

    .action-key {
        padding: 0 8px;
        font-size: 12px;
    }

    .desktop-hint {
        display: none;
    }

    .mobile-hint {
        display: inline;
    }

    .input-section {
        padding: 15px;
    }

    .letters-container {
        gap: 8px;
    }

    .letter-slot {
        width: 35px;
        height: 45px;
    }

    .user-letter {
        font-size: 1.2rem;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .letters-container {
        gap: 6px;
    }

    .letter-slot {
        width: 30px;
        height: 40px;
    }

    .user-letter {
        font-size: 1rem;
    }

    .key-btn {
        min-width: 28px;
        height: 38px;
        font-size: 13px;
    }

    .keyboard-row {
        gap: 3px;
    }

    .action-key {
        padding: 0 6px;
        font-size: 11px;
    }
}